---
import { type CollectionEntry } from "astro:content";

// component imports
import BaseHead from "@layouts/BaseHead.astro";
import Nav from "@components/Nav/Nav.astro";
import Footer from "@components/Footer/Footer.astro";
import CookieConsent from "@components/CookieConsent/CookieConsent.astro";
import MobileNavContent from "@components/Nav/MobileNav/MobileNavContent.astro";

// style import
import "@/styles/global.css";
// data
import siteSettings from "@config/siteSettings.json";
import { type LocationData } from "@config/locations.ts";

// heroImage and authorData are defined on blog posts
// authorData could also be defined on the about author page
interface Props {
	type?: "blog" | "general" | "category";
	title: string;
	description: string;
	image?: ImageMetadata;
	authorsData?: CollectionEntry<"authors">[];
	postFrontmatter?: CollectionEntry<"blog">["data"];
	category?: string;
	postCount?: number;
	noindex?: boolean; // you need to opt-in to no indexing, as it hides the page from search engines
	primaryLocation?: LocationData; // New optional location prop
}

const {
	type = "general",
	title,
	description,
	image,
	authorsData,
	postFrontmatter,
	category,
	postCount,
	noindex = false,
	primaryLocation,
} = Astro.props as Props;

const currLocale = "it";
---

<!doctype html>
<html lang={currLocale} transition:animate="fade">
	<head>
		<BaseHead
			type={type}
			title={title}
			description={description}
			image={image ? image : undefined}
			authors={authorsData ? authorsData : undefined}
			postFrontmatter={postFrontmatter ? postFrontmatter : undefined}
			category={category}
			postCount={postCount}
			noindex={noindex}
			primaryLocation={primaryLocation}
		/>
	</head>
	<body id="body" class={` ${siteSettings.useAnimations === true ? "use-animations" : ""}`}>
		<!-- put cookie consent first so it is seen first by screen readers -->
		<CookieConsent />

		<!-- Mobile Menu Content -->
		<MobileNavContent />

		<div class="min-h-[100lvh]">
			<Nav />
			<main>
				<slot />
			</main>
		</div>
		<Footer />



		<!-- Tarteaucitron initialization -->
		<script is:inline>
			function initTarteaucitron() {
				if (typeof tarteaucitron !== 'undefined' && !window.tarteaucitronInitialized) {
					console.log('Initializing tarteaucitron...');

					// Set Italian translations
					tarteaucitron.lang = {
						"middleBarHead": "☝️ 🍪",
						"adblock": "Benvenuto! Questo sito ti permette di attivare i servizi di terzi di tua scelta.",
						"adblock_call": "Disabilita il tuo adblocker per iniziare la navigazione.",
						"reload": "Aggiorna la pagina",
						"alertBigScroll": "Continuando a scorrere,",
						"alertBigClick": "Continuando a navigare nel sito,",
						"alertBig": "autorizzi l'utilizzo dei cookies inviati da domini di terze parti",
						"alertBigPrivacy": "Questo sito fa uso di cookies e ti consente di decidere se accettarli o rifiutarli",
						"alertSmall": "Gestione dei servizi",
						"acceptAll": "Ok, accetta tutto",
						"personalize": "Personalizza",
						"close": "Chiudi",
						"closeBanner": "Nascondi il banner dei cookie",
						"privacyUrl": "Politica sulla riservatezza",
						"all": "Preferenze per tutti i servizi",
						"info": "Proteggere la tua privacy",
						"disclaimer": "Autorizzando questi servizi di terze parti, accetti il deposito e la lettura di cookies e l'utilizzo di tecnologie di tracciamento necessarie al loro corretto funzionamento.",
						"allow": "Autorizza",
						"deny": "Rifiuta",
						"noCookie": "Questo servizio non deposita cookies.",
						"useCookie": "Questo servizio può depositare",
						"useCookieCurrent": "Questo servizio ha depositato",
						"useNoCookie": "Questo servizio non ha depositato nessun cookie.",
						"more": "Scopri di più",
						"source": "Visualizza il sito ufficiale",
						"credit": "Gestione dei cookies da tarteaucitron.js",
						"noServices": "Questo sito non utilizza nessun cookie che richieda il tuo consenso.",
						"toggleInfoBox": "Mostra/nascondi informazioni",
						"title": "Pannello di gestione dei cookies",
						"cookieDetail": "Dettaglio dei cookies per",
						"ourSite": "sul nostro sito",
						"modalWindow": "(finestra modale)",
						"newWindow": "(nuova finestra)",
						"allowAll": "Autorizza tutti i cookies",
						"denyAll": "Rifiuta tutti i cookies"
					};

					// Add a test service BEFORE initialization
					tarteaucitron.user.gtagUa = 'G-TEST123';
					(tarteaucitron.job = tarteaucitron.job || []).push('gtag');

					tarteaucitron.init({
						"privacyUrl": "/privacy-policy",
						"bodyPosition": "bottom",
						"hashtag": "#tarteaucitron",
						"cookieName": "tarteaucitron",
						"orientation": "bottom",
						"groupServices": false,
						"showDetailsOnClick": true,
						"serviceDefaultState": "wait",
						"showAlertSmall": true,
						"cookieslist": false,
						"cookieslistEmbed": false,
						"showIcon": true,
						"iconPosition": "BottomRight",
						"adblocker": false,
						"DenyAllCta": true,
						"AcceptAllCta": true,
						"highPrivacy": false,
						"alwaysNeedConsent": true,
						"handleBrowserDNTRequest": false,
						"removeCredit": false,
						"moreInfoLink": true,
						"useExternalCss": false,
						"useExternalJs": false,
						"readmoreLink": "",
						"mandatory": true,
						"mandatoryCta": true,
						"googleConsentMode": true,
						"bingConsentMode": true,
						"softConsentMode": false,
						"dataLayer": false,
						"serverSide": false,
						"partnersList": false
					});

					// Force UI initialization
					setTimeout(function() {
						console.log('Forcing UI initialization...');
						if (tarteaucitron.userInterface && tarteaucitron.userInterface.init) {
							tarteaucitron.userInterface.init();
						}
						// Check if elements were created
						console.log('Banner elements after init:', document.querySelectorAll('[id*="tarteaucitron"]').length);
					}, 100);

					window.tarteaucitronInitialized = true;
					console.log('Tarteaucitron initialized successfully');
				}
			}

			// Initialize on DOM ready
			if (document.readyState === 'loading') {
				document.addEventListener('DOMContentLoaded', initTarteaucitron);
			} else {
				initTarteaucitron();
			}

			// Also initialize on astro page load for view transitions
			document.addEventListener('astro:page-load', initTarteaucitron);
		</script>

		<!-- Scroll animations -->
		<script>
			import siteSettings from "@config/siteSettings.json";

			import AOS from "@js/aos/aos";

			if (siteSettings.useAnimations === true) {
				// runs on initial page load
				AOS.init({ once: true, duration: 0.75, distance: 100, offset: 120 });

				// runs on view transitions navigation
				document.addEventListener("astro:after-swap", AOS.refreshHard);
			}
		</script>
	</body>
</html>