---
import { type CollectionEntry } from "astro:content";
import { ClientRouter } from "astro:transitions";

// component imports
import Seo from "@components/Seo/Seo.astro";

// data
import siteSettings from "@config/siteSettings.json";
import { type LocationData } from "@config/locations.ts";

export interface Props {
	type?: "blog" | "general" | "category" | undefined;
	title: string;
	description: string;
	image?: ImageMetadata;
	postFrontmatter?: CollectionEntry<"blog">["data"];
	authors?: CollectionEntry<"authors">[];
	category?: string;
	postCount?: number;
	noindex?: boolean;
	primaryLocation?: LocationData;
}

const {
	type = "general",
	title,
	description,
	image,
	authors,
	postFrontmatter,
	category,
	postCount,
	noindex = false,
	primaryLocation,
} = Astro.props as Props;

import Poppins400 from "@fontsource/poppins/files/poppins-latin-400-normal.woff2";
import Poppins500 from "@fontsource/poppins/files/poppins-latin-500-normal.woff2";
import Poppins600 from "@fontsource/poppins/files/poppins-latin-600-normal.woff2";
import Poppins700 from "@fontsource/poppins/files/poppins-latin-700-normal.woff2";
---

<meta charset="utf-8" />
<meta name="viewport" content="width=device-width" initial-scale="1.0" />

<!-- local font preload for better performance -->
<link rel="preload" href={Poppins400} as="font" type="font/woff2" crossorigin="anonymous" />
<link rel="preload" href={Poppins500} as="font" type="font/woff2" crossorigin="anonymous" />
<link rel="preload" href={Poppins600} as="font" type="font/woff2" crossorigin="anonymous" />
<link rel="preload" href={Poppins700} as="font" type="font/woff2" crossorigin="anonymous" />

<!-- Favicons. I store these in public/favicons. Wherever you store them in the public folder is what you need these href's to match -->
<link rel="icon" href="/favicons/favicon.ico?v=2025" />
<!-- Below is generated from https://realfavicongenerator.net/ -->
<link rel="apple-touch-icon" sizes="180x180" href="/favicons/apple-touch-icon.png?v=2025" />
<link rel="icon" type="image/png" sizes="32x32" href="/favicons/favicon-32x32.png?v=2025" />
<link rel="icon" type="image/png" sizes="16x16" href="/favicons/favicon-16x16.png?v=2025" />
<link rel="manifest" href="/favicons/site.webmanifest?v=2025" />
<link rel="mask-icon" href="/favicons/safari-pinned-tab.svg?v=2025" color="#006A71" />
<link rel="shortcut icon" href="/favicons/favicon.ico?v=2025" />
<meta name="msapplication-TileColor" content="#ffffff" />
<meta name="msapplication-config" content="/favicons/browserconfig.xml" />
<meta name="theme-color" content="#006A71" />
<!-- end favicons -->

<!-- other -->
<meta name="generator" content={Astro.generator} />
<link rel="sitemap" href="/sitemap-index.xml" />

<!-- Theme change setup. Run early to eliminate Flash of inAccurate coloR Theme (FART) -->
<!-- DARK MODE TEMPORARILY DISABLED - To re-enable, restore the original initTheme function -->
<script is:inline>
	function initTheme() {
		// TEMPORARY: Force light theme only
		document.documentElement.classList.remove("dark");
		localStorage.setItem("colorTheme", "light");

		/* ORIGINAL CODE - COMMENTED OUT FOR TEMPORARY DISABLE
		const colorTheme = localStorage.getItem("colorTheme");
		if (!colorTheme) {
			// if no color theme yet, use the users browser preferences
			if (window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches) {
				document.documentElement.classList.add("dark");
				localStorage.setItem("colorTheme", "dark");
			} else {
				document.documentElement.classList.remove("dark");
				localStorage.setItem("colorTheme", "light");
			}
		} else {
			// If there is, assign the theme based on the value in local storage
			if (colorTheme === "dark") {
				document.documentElement.classList.add("dark");
			} else if (colorTheme === "light") {
				document.documentElement.classList.remove("dark");
			}
		}
		*/
	}

	// runs on initial page load
	initTheme();

	// runs on view transitions navigation
	document.addEventListener("astro:after-swap", initTheme);
</script>

<link rel="stylesheet" href="/tarteaucitron/css/tarteaucitron.css">
<script type="text/javascript" src="/tarteaucitron/tarteaucitron.it.js"></script>
<script type="text/javascript" src="/tarteaucitron/tarteaucitron.services.js"></script>


<Seo
	type={type}
	title={title}
	description={description}
	image={image}
	authors={authors}
	postFrontmatter={postFrontmatter}
	category={category}
	postCount={postCount}
	noindex={noindex}
	primaryLocation={primaryLocation}
/>

<!-- no fallback as I saw issues with firefox fallback -->
{siteSettings.useViewTransitions && <ClientRouter fallback="none" />}