---
import { Icon } from "astro-icon/components";

// components
import NavLink from "@components/Nav/NavLink.astro";

// data
import { type navMegaDropdownColumn, type navDropdownItem, type navMegaDropdownItem } from "@config/types/configDataTypes";

interface Props {
	navItem: navDropdownItem | navMegaDropdownColumn | navMegaDropdownItem;
}

const { navItem } = Astro.props as Props;

let dropdownItem: navDropdownItem;

if ("dropdown" in navItem) {
	// it is a normal dropdown item
	dropdownItem = navItem;
} else if ("megaMenuColumns" in navItem) {
	// it is a mega menu dropdown item, flatten all items from all columns
	const allItems = navItem.megaMenuColumns.flatMap(column => column.items);
	dropdownItem = {
		text: navItem.text,
		dropdown: allItems,
	};
} else {
	// it is a mega menu dropdown column, so mock a dropdown item
	dropdownItem = {
		text: navItem.title,
		dropdown: navItem.items,
	};
}
---

<li class="mobile-nav__dropdown group relative">
	<button
		class="primary-focus mobile-nav__dropdown-button nav__link--base flex w-full flex-1 items-start justify-between rounded-md px-4 py-3 text-left duration-300 leading-tight text-base-900 dark:text-base-100 hover:bg-base-100 dark:hover:bg-base-800"
		type="button"
		aria-label={`${dropdownItem.text} dropdown menu`}
		aria-expanded="false"
		aria-haspopup="true"
	>
		{dropdownItem.text}
		<Icon
			name="tabler/chevron-down"
			aria-hidden="true"
			class="mobile-nav__dropdown-chevron mt-1 size-6 flex-shrink-0 transition-transform duration-300"
		/>
	</button>

	<div
		class="mobile-nav__dropdown-content hidden max-h-0 overflow-hidden transition-all duration-300 ease-in-out"
	>
		<ul class="mb-3 space-y-1 transition-[height] duration-300">
			{
				dropdownItem.dropdown.map((dropdownItem) => (
					// no icons for mobile dropdown. I think this looks better
					<NavLink navItem={dropdownItem} noIcon={true} class="ml-2 text-sm" />
				))
			}
		</ul>
	</div>
</li>

<script>
	function mobileNavDropdownSetup() {
		const dropdownMenus = document.querySelectorAll(
			".mobile-nav__dropdown",
		) as NodeListOf<HTMLElement>;
		dropdownMenus.forEach((dropdownMenu) => {
			const dropdownButton = dropdownMenu.querySelector(
				".mobile-nav__dropdown-button",
			) as HTMLElement;
			const dropdownChevron = dropdownMenu.querySelector(
				".mobile-nav__dropdown-chevron",
			) as HTMLElement;
			const dropdownContent = dropdownMenu.querySelector(
				".mobile-nav__dropdown-content",
			) as HTMLElement;

			if (dropdownButton && dropdownContent && dropdownChevron) {
				dropdownButton.addEventListener("click", (event) => {
					if (!dropdownMenu.classList.contains("active")) {
						// if dropdown is currently closed, so open it
						dropdownMenu.classList.add("active");
						dropdownButton.setAttribute("aria-expanded", "true");

						// set max-height to the height of the dropdown content
						// this makes it animate properly
						dropdownContent.classList.remove("hidden");
						dropdownContent.style.maxHeight = dropdownContent.scrollHeight + "px";
						dropdownChevron.classList.add("rotate-180");

						// close all other dropdowns
						dropdownMenus.forEach((otherDropdownMenu) => {
							if (otherDropdownMenu !== dropdownMenu) {
								otherDropdownMenu.classList.remove("active");
								otherDropdownMenu
									.querySelector(".mobile-nav__dropdown-button")
									?.setAttribute("aria-expanded", "false");
								otherDropdownMenu
									.querySelector(".mobile-nav__dropdown-content")
									?.setAttribute("style", "max-height: 0px");
								otherDropdownMenu
									.querySelector(".mobile-nav__dropdown-chevron")
									?.classList.remove("rotate-180");
								setTimeout(() => {
									otherDropdownMenu
										.querySelector(".mobile-nav__dropdown-content")
										?.classList.add("hidden");
								}, 300);
							}
						});
					} else {
						// dropdown is currently open, so close it
						dropdownMenu.classList.remove("active");
						dropdownButton.setAttribute("aria-expanded", "false");

						// set max-height to the height of the dropdown content
						// this makes it animate properly
						dropdownContent.style.maxHeight = "0px";
						dropdownChevron.classList.remove("rotate-180");
						// delay to allow close animation
						setTimeout(() => {
							dropdownContent.classList.add("hidden");
						}, 300);
					}
					event.preventDefault();
					return false;
				});
			}
		});
	}

	// runs on initial page load
	mobileNavDropdownSetup();

	// runs on view transitions navigation
	document.addEventListener("astro:after-swap", mobileNavDropdownSetup);
</script>